import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
  Switch,
  Platform,
  FlatList,
  SafeAreaView,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { X, Calendar, ArrowLeft } from 'lucide-react-native';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import {
  fetchListingTypes,
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';
import Dropdown from '@/components/Dropdown';
import MultiSelectDropdown from '@/components/MultiSelectDropdown';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
}

interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[]; // Array of listing IDs
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function EditStatusScreen() {
  // Get params from navigation
  const { statuses: statusesParam, selectedStatus: selectedStatusParam } = useLocalSearchParams();
  
  // Parse statuses from params (in real app, you'd fetch these)
  const statuses: StatusOption[] = statusesParam ? JSON.parse(statusesParam as string) : [];
  const selectedStatus = selectedStatusParam as string || '';

  const [tempSelectedStatus, setTempSelectedStatus] = useState(selectedStatus);
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [showOfferNegotiationConfig, setShowOfferNegotiationConfig] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp'>('meeting');

  // Fetch dropdown data for viewing configuration
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: listingTypes = [] } = useQuery({
    queryKey: ['listingTypes'],
    queryFn: fetchListingTypes,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format - only using available endpoints
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  // All dropdown options are now imported from constants

  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });
  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
  });

  // Pagination constants and state
  const ITEMS_PER_PAGE = 10;
  const queryClient = useQueryClient();
  const [currentViewingPage, setCurrentViewingPage] = useState(1);
  const [currentOfferPage, setCurrentOfferPage] = useState(1);
  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });
  const [offerNegotiationConfig, setOfferNegotiationConfig] = useState<OfferNegotiationConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
  });
  const enabledStatuses = statuses.filter(status => status.is_disabled === 0);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true, // Always enabled, but returns default when no location
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);

  // Fallback to static options if no towers
  const finalTowerOptions = towerBuildingOptions.length > 0 ? towerBuildingOptions : [
    { id: 'any', label: 'Any' },
    { id: 'test', label: 'Test Tower' }
  ];

  // Create filters object for listings API
  const createListingFilters = (config: ViewingConfig | OfferNegotiationConfig) => {
    const filters: any = {};

    // Map location
    if (config.search) {
      filters.location = config.search;
    }

    // Map rent/sale to adType
    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    return filters;
  };

  // Check if any filters are applied
  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale || viewingConfig?.towerBuilding ||
                    (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) || viewingConfig?.minArea || viewingConfig?.maxArea ||
                    viewingConfig?.priceMin || viewingConfig?.priceMax;

  // Create query key without selectedProperties to avoid re-fetching on selection changes
  const viewingConfigForQuery = {
    search: viewingConfig.search,
    rentSale: viewingConfig.rentSale,
    towerBuilding: viewingConfig.towerBuilding,
    bedrooms: viewingConfig.bedrooms,
    minArea: viewingConfig.minArea,
    maxArea: viewingConfig.maxArea,
    priceMin: viewingConfig.priceMin,
    priceMax: viewingConfig.priceMax,
    // Exclude selectedProperties from query key
  };

  // Fetch listings with pagination
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['modal-listings', viewingConfigForQuery, currentViewingPage],
    queryFn: () => {
      // If no filters applied, fetch all listings with default inventory filters
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        console.log('🔍 Fetching listings page', currentViewingPage, 'with default params:', params);
        return fetchListings(currentViewingPage, params);
      }
      // If filters applied, use them with vt filter
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      console.log('🔍 Fetching listings page', currentViewingPage, 'with filtered params:', params);
      return fetchListings(currentViewingPage, params);
    },
    enabled: showViewingConfig, // Only fetch when viewing config is shown
  });

  // Extract listings data
  const listings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalViewingPages = Math.ceil(totalListings / ITEMS_PER_PAGE);



  useEffect(() => {
    setTempSelectedStatus(selectedStatus);
  }, [selectedStatus]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const handleStatusToggle = (statusId: string) => {
    // Always select the new status (no deselection)
    setTempSelectedStatus(statusId);

    // Reset all filters and queries when switching status
    console.log('🔄 Status changed - resetting all filters and queries...');
    resetAllFiltersAndQueries();

    // Check if MEETING_SCHEDULED, VIEWING_SCHEDULED, FOLLOW_UP, or OFFER_NEGOTIATION is selected
    const selectedStatusObj = statuses.find(s => s.id.toString() === statusId);
    if (selectedStatusObj && selectedStatusObj.name === 'MEETING_SCHEDULED') {
      setShowMeetingConfig(true);
      setShowViewingConfig(false);
      setShowFollowUpConfig(false);
      setShowOfferNegotiationConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'VIEWING_SCHEDULED') {
      setShowViewingConfig(true);
      setShowMeetingConfig(false);
      setShowFollowUpConfig(false);
      setShowOfferNegotiationConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'FOLLOW_UP') {
      setShowFollowUpConfig(true);
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
      setShowOfferNegotiationConfig(false);
    } else if (selectedStatusObj && selectedStatusObj.name === 'OFFER_NEGOTIATION') {
      setShowOfferNegotiationConfig(true);
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
      setShowFollowUpConfig(false);
    } else {
      setShowMeetingConfig(false);
      setShowViewingConfig(false);
      setShowFollowUpConfig(false);
      setShowOfferNegotiationConfig(false);
    }
  };

  const handleSave = () => {
    if (tempSelectedStatus) {
      const selectedStatusObj = statuses.find(s => s.id.toString() === tempSelectedStatus);
      // In a real implementation, you would call the onSelectStatus callback here
      // For now, just navigate back
      console.log('Saving status:', tempSelectedStatus);
      if (selectedStatusObj && selectedStatusObj.name === 'MEETING_SCHEDULED') {
        console.log('Meeting config:', meetingConfig);
      } else if (selectedStatusObj && selectedStatusObj.name === 'VIEWING_SCHEDULED') {
        console.log('Viewing config:', viewingConfig);
      } else if (selectedStatusObj && selectedStatusObj.name === 'FOLLOW_UP') {
        console.log('Follow up config:', followUpConfig);
      } else if (selectedStatusObj && selectedStatusObj.name === 'OFFER_NEGOTIATION') {
        console.log('Offer negotiation config:', offerNegotiationConfig);
      }
    }
    router.back();
  };

  const updateMeetingConfig = (field: keyof MeetingConfig, value: string | boolean) => {
    setMeetingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[] | (string | number)[]) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateOfferNegotiationConfig = (field: keyof OfferNegotiationConfig, value: string | string[] | (string | number)[]) => {
    setOfferNegotiationConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle listing selection - optimized with useCallback
  const toggleListingSelection = useCallback((listingId: string) => {
    console.log('✅ Checkbox toggle for listing:', listingId);
    setViewingConfig(prev => ({
      ...prev,
      selectedProperties: prev.selectedProperties.includes(listingId)
        ? prev.selectedProperties.filter(id => id !== listingId)
        : [...prev.selectedProperties, listingId]
    }));
  }, []);

  // Optimized render item for FlatList
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            viewingConfig.selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {viewingConfig.selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [viewingConfig.selectedProperties, toggleListingSelection]);

  // Optimized getItemLayout for better performance
  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90, // Approximate height of each compact listing item
    offset: 90 * index,
    index,
  }), []);

  const handleDatePress = (configType: 'meeting' | 'followUp') => {
    console.log('Date picker pressed for:', configType);
    setCurrentConfigType(configType);
    setDatePickerMode('date');
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    // Închide date picker-ul automat după selecție
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      if (currentConfigType === 'meeting') {
        updateMeetingConfig('dueDate', formattedDate);
      } else if (currentConfigType === 'followUp') {
        updateFollowUpConfig('dueDate', formattedDate);
      }
    }
  };

  // Reset all filters and queries function
  const resetAllFiltersAndQueries = () => {
    console.log('🔄 Resetting all filters and queries...');

    // Reset viewing config
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });

    // Reset offer negotiation config
    setOfferNegotiationConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
    });

    // Reset pagination to first page
    setCurrentViewingPage(1);
    setCurrentOfferPage(1);

    // Reset pagination to first page
    setCurrentViewingPage(1);
    setCurrentOfferPage(1);

    // Remove all cached queries to start fresh
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
    queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#111827" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Select Status</Text>
              <View style={styles.backButton} />
            </View>
          ),
        }}
      />

      <View style={styles.content}>
        {/* Status Pills */}
        <ScrollView
          horizontal
          style={styles.statusScrollContainer}
          contentContainerStyle={styles.statusScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {enabledStatuses.map((status) => (
            <TouchableOpacity
              key={status.id}
              style={[
                styles.statusPill,
                tempSelectedStatus === status.id.toString() && styles.selectedStatusPill
              ]}
              onPress={() => handleStatusToggle(status.id.toString())}
            >
              <Text
                style={[
                  styles.statusPillText,
                  tempSelectedStatus === status.id.toString() && styles.selectedStatusPillText
                ]}
              >
                {formatStatusName(status.name)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Meeting Configuration */}
        {showMeetingConfig && (
          <ScrollView style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={meetingConfig.title}
                onChangeText={(text) => updateMeetingConfig('title', text)}
                placeholder="Meeting appointment"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={meetingConfig.content}
                onChangeText={(text) => updateMeetingConfig('content', text)}
                placeholder="Meeting appointment reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={meetingConfig.dueDate}
                  onChangeText={(text) => updateMeetingConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={() => {
                    setCurrentConfigType('meeting');
                    setDatePickerMode('date');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker for Meeting */}
              {showDatePicker && currentConfigType === 'meeting' && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        const formattedDate = selectedDate.toLocaleDateString('en-GB', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                        }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: false,
                        });
                        updateMeetingConfig('dueDate', formattedDate);
                      }
                    }}
                  />
                </View>
              )}
              {!meetingConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required.</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{meetingConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <Switch
                value={meetingConfig.sendEmail}
                onValueChange={(value) => updateMeetingConfig('sendEmail', value)}
                trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
                thumbColor={meetingConfig.sendEmail ? '#fff' : '#fff'}
              />
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={meetingConfig.remarks}
              onChangeText={(text) => updateMeetingConfig('remarks', text)}
              placeholder="Remarks"
              multiline
              numberOfLines={3}
            />
          </ScrollView>
        )}

        {/* Follow Up Configuration */}
        {showFollowUpConfig && (
          <ScrollView style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Reminder Section */}
            <Text style={styles.sectionTitle}>Reminder</Text>

            {/* Title */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Title</Text>
              <TextInput
                style={styles.textInput}
                value={followUpConfig.title}
                onChangeText={(text) => updateFollowUpConfig('title', text)}
                placeholder="Follow up"
              />
            </View>

            {/* Content */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Content</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={followUpConfig.content}
                onChangeText={(text) => updateFollowUpConfig('content', text)}
                placeholder="Follow up reminder."
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Due Date</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={[styles.textInput, styles.dateInput]}
                  value={followUpConfig.dueDate}
                  onChangeText={(text) => updateFollowUpConfig('dueDate', text)}
                  placeholder="dd.mm.yyyy, --:--"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={() => {
                    setCurrentConfigType('followUp');
                    setDatePickerMode('date');
                    setShowDatePicker(true);
                  }}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker for Follow Up */}
              {showDatePicker && currentConfigType === 'followUp' && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={new Date()}
                    mode="date"
                    display="default"
                    onChange={(event, selectedDate) => {
                      setShowDatePicker(false);
                      if (selectedDate) {
                        const formattedDate = selectedDate.toLocaleDateString('en-GB', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                        }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: false,
                        });
                        updateFollowUpConfig('dueDate', formattedDate);
                      }
                    }}
                  />
                </View>
              )}
              {!followUpConfig.dueDate && (
                <Text style={styles.errorText}>Due Date is required</Text>
              )}
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Priority</Text>
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>{followUpConfig.priority}</Text>
                <Text style={styles.dropdownArrow}>▼</Text>
              </TouchableOpacity>
            </View>

            {/* Send Email */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => updateFollowUpConfig('sendEmail', !followUpConfig.sendEmail)}
              >
                {followUpConfig.sendEmail && <Text style={styles.checkmark}>✓</Text>}
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>Send Email</Text>
            </View>

            {/* Remarks Section */}
            <Text style={styles.sectionTitle}>Remarks</Text>

            {/* Remarks */}
            <View style={styles.inputContainer}>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={followUpConfig.remarks}
                onChangeText={(text) => updateFollowUpConfig('remarks', text)}
                placeholder="Remarks"
                multiline
                numberOfLines={3}
              />
            </View>
          </ScrollView>
        )}

        {/* Viewing Configuration */}
        {showViewingConfig && (
          <ScrollView style={styles.meetingConfigContainer} showsVerticalScrollIndicator={false}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* Location Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Location</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {locationOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterPill,
                      viewingConfig.search === option.id.toString() && styles.selectedFilterPill
                    ]}
                    onPress={() => {
                      // Allow deselection - if already selected, deselect it
                      const newValue = viewingConfig.search === option.id.toString() ? '' : option.id.toString();
                      updateViewingConfig('search', newValue);
                      // Reset tower/building when location changes or is deselected
                      updateViewingConfig('towerBuilding', '');
                    }}
                  >
                    <Text
                      style={[
                        styles.filterPillText,
                        viewingConfig.search === option.id.toString() && styles.selectedFilterPillText
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Tower/Building Row - Only show when location is selected */}
            {viewingConfig.search && (
              <View style={styles.fullWidthContainer}>
                <Text style={styles.inputLabel}>Tower/Building</Text>
                <ScrollView
                  horizontal
                  style={styles.pillScrollContainer}
                  contentContainerStyle={styles.pillScrollContent}
                  showsHorizontalScrollIndicator={false}
                >
                  {finalTowerOptions.map((option) => {
                    const isSelected = viewingConfig.towerBuilding === option.id.toString();
                    return (
                      <TouchableOpacity
                        key={option.id}
                        style={[
                          styles.filterPill,
                          isSelected && styles.selectedFilterPill
                        ]}
                        onPress={() => {
                          // Allow deselection - if already selected, deselect it
                          const newValue = isSelected ? '' : option.id.toString();
                          updateViewingConfig('towerBuilding', newValue);
                        }}
                      >
                        <Text
                          style={[
                            styles.filterPillText,
                            isSelected && styles.selectedFilterPillText
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </ScrollView>
              </View>
            )}

            {/* Rent/Sale Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Rent/Sale</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {RENT_SALE_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.rentSale === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('rentSale', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Min Area Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Min Area</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {MIN_AREA_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.minArea === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('minArea', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Max Area Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Max Area</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {MAX_AREA_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.maxArea === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('maxArea', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Price Min Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Price Min</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {PRICE_MIN_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.priceMin === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('priceMin', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Price Max Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Price Max</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {PRICE_MAX_OPTIONS.map((option) => {
                  const isSelected = viewingConfig.priceMax === option.id.toString();
                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.filterPill,
                        isSelected && styles.selectedFilterPill
                      ]}
                      onPress={() => {
                        // Allow deselection - if already selected, deselect it
                        const newValue = isSelected ? '' : option.id.toString();
                        updateViewingConfig('priceMax', newValue);
                      }}
                    >
                      <Text
                        style={[
                          styles.filterPillText,
                          isSelected && styles.selectedFilterPillText
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>

            {/* Bedrooms Row */}
            <View style={styles.fullWidthContainer}>
              <Text style={styles.inputLabel}>Bedrooms</Text>
              <ScrollView
                horizontal
                style={styles.pillScrollContainer}
                contentContainerStyle={styles.pillScrollContent}
                showsHorizontalScrollIndicator={false}
              >
                {bedroomOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.filterPill,
                      viewingConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPill
                    ]}
                    onPress={() => {
                      const currentBedrooms = viewingConfig.bedrooms;
                      const bedroomId = option.id.toString();
                      const newBedrooms = currentBedrooms.includes(bedroomId)
                        ? currentBedrooms.filter(id => id !== bedroomId)
                        : [...currentBedrooms, bedroomId];
                      updateViewingConfig('bedrooms', newBedrooms);
                    }}
                  >
                    <Text
                      style={[
                        styles.filterPillText,
                        viewingConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPillText
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Viewing Buttons */}
            <View style={styles.viewingButtonsContainer}>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={() => {
                  console.log('🔄 Resetting viewing filters...');
                  setViewingConfig({
                    search: '',
                    rentSale: '',
                    towerBuilding: '',
                    bedrooms: [],
                    minArea: '',
                    maxArea: '',
                    priceMin: '',
                    priceMax: '',
                    selectedProperties: [],
                  });
                  setCurrentViewingPage(1);
                  queryClient.removeQueries({ queryKey: ['modal-listings'] });
                }}
              >
                <Text style={styles.resetButtonText}>Reset filters</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.scheduleButton,
                  viewingConfig.selectedProperties.length === 0 && styles.scheduleButtonDisabled
                ]}
                disabled={viewingConfig.selectedProperties.length === 0}
                onPress={() => {
                  if (viewingConfig.selectedProperties.length > 0) {
                    console.log('Scheduling view for properties:', viewingConfig.selectedProperties);
                    // Here you can handle the scheduling logic
                  }
                }}
              >
                <Text style={[
                  styles.scheduleButtonText,
                  viewingConfig.selectedProperties.length === 0 && styles.scheduleButtonTextDisabled
                ]}>
                  Schedule view
                  {viewingConfig.selectedProperties.length > 0 && ` (${viewingConfig.selectedProperties.length})`}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Listings Results */}
            {isLoadingListings ? (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>Loading properties...</Text>
              </View>
            ) : listings.length > 0 ? (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>
                  Properties ({listings.length} din {totalListings})
                </Text>
                <View style={styles.listingsList}>
                  {listings.map((item) => (
                    <View key={item.id.toString()} style={styles.listingCard}>
                      <Text style={styles.listingTitle}>{item.title}</Text>
                      <Text style={styles.listingLocation}>{item.location}</Text>
                      <Text style={styles.listingPrice}>{item.price}</Text>
                    </View>
                  ))}
                  {totalViewingPages > 1 && (
                    <View style={styles.paginationContainer}>
                      <Pagination
                        currentPage={currentViewingPage}
                        totalPages={totalViewingPages}
                        onPageChange={(page) => setCurrentViewingPage(page)}
                      />
                    </View>
                  )}
                </View>
              </View>
            ) : (
              <View style={styles.listingsContainer}>
                <Text style={styles.listingsTitle}>No properties found</Text>
              </View>
            )}
          </ScrollView>
        )}

        {/* Offer Negotiation Configuration */}
        {showOfferNegotiationConfig && (
          <ScrollView style={styles.meetingConfigContainer}>
            <Text style={styles.meetingConfigTitle}>Lead configuration</Text>

            {/* First Row: Location and Tower/Building */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Location"
                  options={locationOptions}
                  selectedValue={offerNegotiationConfig.search}
                  onSelect={(option) => updateOfferNegotiationConfig('search', option.id.toString())}
                  placeholder="Search location"
                />
              </View>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Tower/Building"
                  options={finalTowerOptions}
                  selectedValue={offerNegotiationConfig.towerBuilding}
                  onSelect={(option) => updateOfferNegotiationConfig('towerBuilding', option.id.toString())}
                  placeholder="Tower/Building"
                  searchable={true}
                  searchPlaceholder="Search towers..."
                />
              </View>
            </View>

            {/* Second Row: Rent/Sale and Min Area */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Rent/Sale"
                  options={RENT_SALE_OPTIONS}
                  selectedValue={offerNegotiationConfig.rentSale}
                  onSelect={(option) => updateOfferNegotiationConfig('rentSale', option.id.toString())}
                  placeholder="Select Rent/Sale"
                />
              </View>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Min Area"
                  options={MIN_AREA_OPTIONS}
                  selectedValue={offerNegotiationConfig.minArea}
                  onSelect={(option) => updateOfferNegotiationConfig('minArea', option.id.toString())}
                  placeholder="Select Min Area"
                />
              </View>
            </View>

            {/* Third Row: Max Area and Price Min */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Max Area"
                  options={MAX_AREA_OPTIONS}
                  selectedValue={offerNegotiationConfig.maxArea}
                  onSelect={(option) => updateOfferNegotiationConfig('maxArea', option.id.toString())}
                  placeholder="Select Max Area"
                />
              </View>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Price Min"
                  options={PRICE_MIN_OPTIONS}
                  selectedValue={offerNegotiationConfig.priceMin}
                  onSelect={(option) => updateOfferNegotiationConfig('priceMin', option.id.toString())}
                  placeholder="Select Price Min"
                />
              </View>
            </View>

            {/* Fourth Row: Price Max and Bedrooms */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Dropdown
                  label="Price Max"
                  options={PRICE_MAX_OPTIONS}
                  selectedValue={offerNegotiationConfig.priceMax}
                  onSelect={(option) => updateOfferNegotiationConfig('priceMax', option.id.toString())}
                  placeholder="Select Price Max"
                />
              </View>
              <View style={styles.halfWidth}>
                <MultiSelectDropdown
                  label="Bedrooms"
                  options={bedroomOptions}
                  selectedValues={offerNegotiationConfig.bedrooms}
                  onSelect={(selectedIds) => updateOfferNegotiationConfig('bedrooms', selectedIds)}
                  placeholder="Select Bedrooms"
                  showConfirmButton={true}
                  confirmButtonText="OK"
                />
              </View>
            </View>

            {/* Reset filters button */}
            <View style={styles.offerNegotiationButtonsContainer}>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={() => {
                  console.log('🔄 Resetting offer negotiation filters...');
                  setOfferNegotiationConfig({
                    search: '',
                    rentSale: '',
                    towerBuilding: '',
                    bedrooms: [],
                    minArea: '',
                    maxArea: '',
                    priceMin: '',
                    priceMax: '',
                  });
                  setCurrentOfferPage(1);
                  queryClient.removeQueries({ queryKey: ['modal-offer-listings'] });
                }}
              >
                <Text style={styles.resetButtonText}>Reset filters</Text>
              </TouchableOpacity>
            </View>

            {/* Properties table placeholder */}
            <View style={styles.propertiesContainer}>
              <Text style={styles.noItemsText}>No items selected</Text>
            </View>
          </ScrollView>
        )}

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.saveButton,
              !tempSelectedStatus && styles.saveButtonDisabled
            ]}
            onPress={handleSave}
            disabled={!tempSelectedStatus}
          >
            <Text style={[
              styles.saveButtonText,
              !tempSelectedStatus && styles.saveButtonTextDisabled
            ]}>
              Save
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    position: 'relative',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    textAlign: 'center',
    flex: 1,
  },
  backButton: {
    width: 40,
    alignItems: 'flex-start',
  },
  content: {
    flex: 1,
  },
  statusScrollContainer: {
    marginVertical: 20,
  },
  statusScrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  statusPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
  // Meeting Configuration Styles
  meetingConfigContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    maxHeight: screenHeight * 0.4,
  },
  meetingConfigTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    marginTop: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateInput: {
    flex: 1,
    paddingRight: 40,
  },
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 0,
    bottom: 0,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: 'transparent',
    padding: 0,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#1F2937',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#1F2937',
    marginLeft: 12,
  },
  // Listing styles
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  // Checkbox styles
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  checkmark: {
    fontSize: 14,
    color: '#B89C4C',
    fontWeight: 'bold',
  },
  // Viewing Configuration Styles
  fullWidthContainer: {
    marginBottom: 16,
  },
  // Filter pill styles
  pillScrollContainer: {
    marginTop: 8,
  },
  pillScrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  filterPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  // Viewing buttons styles
  viewingButtonsContainer: {
    flexDirection: 'row',
    marginTop: 16,
    marginBottom: 16,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  scheduleButton: {
    flex: 2,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    alignItems: 'center',
  },
  scheduleButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  scheduleButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  scheduleButtonTextDisabled: {
    color: '#9CA3AF',
  },
  // Listings styles
  listingsContainer: {
    marginTop: 16,
    maxHeight: 300,
  },
  listingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  listingsList: {
    flex: 1,
  },
  paginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  // Offer Negotiation styles
  rowContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  offerNegotiationButtonsContainer: {
    marginTop: 16,
    marginBottom: 16,
  },
  propertiesContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    alignItems: 'center',
  },
  noItemsText: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  listingCard: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    marginVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  listingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  listingLocation: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  listingPrice: {
    fontSize: 14,
    fontWeight: '500',
    color: '#059669',
  },
});
